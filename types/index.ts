// Core app types
export interface Alarm {
  id: string;
  name: string;
  latitude: number;
  longitude: number;
  radius: number; // in meters
  isActive: boolean;
  soundUri?: string;
  vibrationPattern?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface UserSettings {
  defaultRadius: number;
  defaultSound: string;
  vibrationEnabled: boolean;
  notificationsEnabled: boolean;
  theme: 'light' | 'dark' | 'auto';
}

export interface LocationCoordinates {
  latitude: number;
  longitude: number;
}

export interface MapRegion {
  latitude: number;
  longitude: number;
  latitudeDelta: number;
  longitudeDelta: number;
}

// Background location and geofencing types
export interface BackgroundPermissionStatus {
  granted: boolean;
  canAskAgain: boolean;
  status: string;
}

export interface GeofenceRegion {
  identifier: string;
  latitude: number;
  longitude: number;
  radius: number;
  notifyOnEnter: boolean;
  notifyOnExit: boolean;
}

export interface GeofenceEvent {
  eventType: 'enter' | 'exit';
  region: {
    identifier: string;
    latitude: number;
    longitude: number;
    radius: number;
  };
}

export interface BackgroundLocationState {
  isGeofencingActive: boolean;
  activeGeofences: GeofenceRegion[];
  backgroundPermissionGranted: boolean;
  error: string | null;
}

// Alarm ringing state types
export interface AlarmRingingState {
  isRinging: boolean;
  alarmId: string | null;
  alarmName: string | null;
  soundUri: string | null;
  vibrationPattern: string | null;
}

export interface AlarmTriggerData {
  alarmId: string;
  alarmName: string;
  soundUri?: string;
  vibrationPattern?: string;
  latitude: number;
  longitude: number;
}
