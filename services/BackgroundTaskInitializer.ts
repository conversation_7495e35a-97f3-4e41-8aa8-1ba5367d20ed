import { Platform } from 'react-native';
import * as Notifications from 'expo-notifications';
import { LOCATION_TASK_NAME } from '@/tasks/locationTask';
import { notificationService } from './NotificationService';

// Configure notification behavior
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: false,
  }),
});

// Initialize background services
export async function initializeBackgroundServices(): Promise<void> {
  try {
    console.log('Initializing background services...');

    // Request notification permissions early using the NotificationService
    await notificationService.requestPermissions();

    // The background task is automatically registered when the locationTask.ts file is imported
    // This happens when the app starts, so we just need to ensure it's imported
    console.log('Background location task registered:', LOCATION_TASK_NAME);

  } catch (error) {
    console.error('Error initializing background services:', error);
  }
}

// Request notification permissions
async function requestNotificationPermissions(): Promise<boolean> {
  try {
    const { status: existingStatus } = await Notifications.getPermissionsAsync();
    let finalStatus = existingStatus;

    if (existingStatus !== 'granted') {
      const { status } = await Notifications.requestPermissionsAsync();
      finalStatus = status;
    }

    if (finalStatus !== 'granted') {
      console.warn('Notification permissions not granted');
      return false;
    }

    // Configure notification channel for Android
    if (Platform.OS === 'android') {
      await Notifications.setNotificationChannelAsync('location-alarms', {
        name: 'Location Alarms',
        importance: Notifications.AndroidImportance.HIGH,
        vibrationPattern: [0, 250, 250, 250],
        lightColor: '#FF231F7C',
        sound: true,
        enableVibrate: true,
      });
    }

    console.log('Notification permissions granted');
    return true;
  } catch (error) {
    console.error('Error requesting notification permissions:', error);
    return false;
  }
}

// Function to check if all required permissions are granted
export async function checkRequiredPermissions(): Promise<{
  notifications: boolean;
  foregroundLocation: boolean;
  backgroundLocation: boolean;
}> {
  try {
    // Check notification permissions
    const { status: notificationStatus } = await Notifications.getPermissionsAsync();

    // Check location permissions (we'll import these dynamically to avoid circular dependencies)
    const { locationService } = await import('@/services/LocationService');
    const foregroundPermissions = await locationService.checkPermissions();
    const backgroundPermissions = await locationService.checkBackgroundPermissions();

    return {
      notifications: notificationStatus === 'granted',
      foregroundLocation: foregroundPermissions.granted,
      backgroundLocation: backgroundPermissions.granted,
    };
  } catch (error) {
    console.error('Error checking permissions:', error);
    return {
      notifications: false,
      foregroundLocation: false,
      backgroundLocation: false,
    };
  }
}

// Function to show permission explanation to user
export function getPermissionExplanation(): {
  foreground: string;
  background: string;
  notifications: string;
} {
  return {
    foreground: 'Snozbuz needs access to your location to create location-based alarms and show your position on the map.',
    background: 'Snozbuz needs background location access to monitor your location and trigger alarms when you enter specific areas, even when the app is not actively open.',
    notifications: 'Snozbuz needs notification permissions to alert you when you enter an alarm area.',
  };
}
