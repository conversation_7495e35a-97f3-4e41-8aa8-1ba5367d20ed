import * as Notifications from 'expo-notifications';
import { Platform } from 'react-native';
import { AlarmTriggerData } from '@/types';

export class NotificationService {
  private static instance: NotificationService;
  private currentNotificationId: string | null = null;

  private constructor() {}

  static getInstance(): NotificationService {
    if (!NotificationService.instance) {
      NotificationService.instance = new NotificationService();
    }
    return NotificationService.instance;
  }

  // Request notification permissions
  async requestPermissions(): Promise<boolean> {
    try {
      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;
      
      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }
      
      if (finalStatus !== 'granted') {
        console.warn('Notification permissions not granted');
        return false;
      }
      
      // Configure notification channel for Android
      if (Platform.OS === 'android') {
        await this.setupAndroidNotificationChannels();
      }
      
      console.log('Notification permissions granted');
      return true;
    } catch (error) {
      console.error('Error requesting notification permissions:', error);
      return false;
    }
  }

  // Setup Android notification channels
  private async setupAndroidNotificationChannels(): Promise<void> {
    // High priority channel for alarm notifications
    await Notifications.setNotificationChannelAsync('alarm-alerts', {
      name: 'Alarm Alerts',
      importance: Notifications.AndroidImportance.MAX,
      vibrationPattern: [0, 250, 250, 250],
      lightColor: '#FF231F7C',
      sound: true,
      enableVibrate: true,
      showBadge: true,
      lockscreenVisibility: Notifications.AndroidNotificationVisibility.PUBLIC,
    });

    // Regular priority channel for general notifications
    await Notifications.setNotificationChannelAsync('general', {
      name: 'General Notifications',
      importance: Notifications.AndroidImportance.DEFAULT,
      vibrationPattern: [0, 250, 250, 250],
      lightColor: '#FF231F7C',
      sound: true,
      enableVibrate: true,
    });
  }

  // Schedule alarm notification
  async scheduleAlarmNotification(alarmData: AlarmTriggerData): Promise<string | null> {
    try {
      // Check if notifications are permitted
      const { status } = await Notifications.getPermissionsAsync();
      if (status !== 'granted') {
        console.warn('Notification permissions not granted');
        return null;
      }

      // Clear any existing alarm notification
      await this.clearAlarmNotification();

      // Schedule immediate high-priority notification
      const notificationId = await Notifications.scheduleNotificationAsync({
        content: {
          title: '🚨 Location Alarm!',
          body: `You've reached "${alarmData.alarmName}"`,
          sound: true,
          priority: Notifications.AndroidNotificationPriority.MAX,
          vibrate: [0, 250, 250, 250],
          data: {
            alarmId: alarmData.alarmId,
            alarmName: alarmData.alarmName,
            latitude: alarmData.latitude,
            longitude: alarmData.longitude,
            type: 'alarm_trigger',
          },
          categoryIdentifier: 'alarm',
          sticky: true, // Keep notification visible
        },
        trigger: null, // Immediate notification
      });

      this.currentNotificationId = notificationId;
      console.log('Alarm notification scheduled:', notificationId);
      return notificationId;
    } catch (error) {
      console.error('Error scheduling alarm notification:', error);
      return null;
    }
  }

  // Clear current alarm notification
  async clearAlarmNotification(): Promise<void> {
    try {
      if (this.currentNotificationId) {
        await Notifications.cancelScheduledNotificationAsync(this.currentNotificationId);
        await Notifications.dismissNotificationAsync(this.currentNotificationId);
        this.currentNotificationId = null;
        console.log('Alarm notification cleared');
      }
    } catch (error) {
      console.error('Error clearing alarm notification:', error);
    }
  }

  // Clear all notifications
  async clearAllNotifications(): Promise<void> {
    try {
      await Notifications.cancelAllScheduledNotificationsAsync();
      await Notifications.dismissAllNotificationsAsync();
      this.currentNotificationId = null;
      console.log('All notifications cleared');
    } catch (error) {
      console.error('Error clearing all notifications:', error);
    }
  }

  // Check if notification permissions are granted
  async hasPermissions(): Promise<boolean> {
    try {
      const { status } = await Notifications.getPermissionsAsync();
      return status === 'granted';
    } catch (error) {
      console.error('Error checking notification permissions:', error);
      return false;
    }
  }

  // Get current notification ID
  getCurrentNotificationId(): string | null {
    return this.currentNotificationId;
  }
}

// Export singleton instance
export const notificationService = NotificationService.getInstance();
