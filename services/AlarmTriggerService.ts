import { Audio } from 'expo-av';
import * as Haptics from 'expo-haptics';
import { router } from 'expo-router';
import { AlarmTriggerData, AlarmRingingState } from '@/types';
import { notificationService } from './NotificationService';

export class AlarmTriggerService {
  private static instance: AlarmTriggerService;
  private currentSound: Audio.Sound | null = null;
  private isPlaying: boolean = false;
  private vibrationInterval: NodeJS.Timeout | null = null;
  private ringingState: AlarmRingingState = {
    isRinging: false,
    alarmId: null,
    alarmName: null,
    soundUri: null,
    vibrationPattern: null,
  };

  private constructor() {}

  static getInstance(): AlarmTriggerService {
    if (!AlarmTriggerService.instance) {
      AlarmTriggerService.instance = new AlarmTriggerService();
    }
    return AlarmTriggerService.instance;
  }

  // Trigger alarm with full functionality
  async triggerAlarm(alarmData: AlarmTriggerData): Promise<void> {
    try {
      console.log('Triggering alarm:', alarmData);

      // Update ringing state
      this.ringingState = {
        isRinging: true,
        alarmId: alarmData.alarmId,
        alarmName: alarmData.alarmName,
        soundUri: alarmData.soundUri || null,
        vibrationPattern: alarmData.vibrationPattern || null,
      };

      // Schedule notification
      await notificationService.scheduleAlarmNotification(alarmData);

      // Start sound
      await this.startSound(alarmData.soundUri);

      // Start vibration
      await this.startVibration(alarmData.vibrationPattern);

      // Navigate to alarm ringing screen
      router.push({
        pathname: '/alarmRinging',
        params: { alarmId: alarmData.alarmId },
      });

      console.log('Alarm triggered successfully');
    } catch (error) {
      console.error('Error triggering alarm:', error);
      // Cleanup on error
      await this.dismissAlarm();
    }
  }

  // Start playing alarm sound
  private async startSound(soundUri?: string): Promise<void> {
    try {
      // Stop any currently playing sound
      await this.stopSound();

      // Configure audio mode for alarm
      await Audio.setAudioModeAsync({
        allowsRecordingIOS: false,
        staysActiveInBackground: true,
        playsInSilentModeIOS: true,
        shouldDuckAndroid: false,
        playThroughEarpieceAndroid: false,
      });

      // Use custom sound if provided, otherwise use system sound
      let soundSource;
      if (soundUri) {
        soundSource = { uri: soundUri };
      } else {
        // Use a system sound or create a simple beep sound
        // For now, we'll use a simple approach without requiring an asset file
        soundSource = null; // Will use system notification sound
      }

      // Load and play sound if soundSource is available
      if (soundSource) {
        const { sound } = await Audio.Sound.createAsync(
          soundSource,
          {
            shouldPlay: true,
            isLooping: true,
            volume: 1.0,
          }
        );

        this.currentSound = sound;
        this.isPlaying = true;
        console.log('Alarm sound started');
      } else {
        // For now, we'll rely on the notification sound and vibration
        // In the future, you can add a default sound file to assets/sounds/
        console.log('Using system notification sound');
      }
    } catch (error) {
      console.error('Error starting alarm sound:', error);
      // Continue without sound - vibration and notification will still work
    }
  }

  // Stop playing alarm sound
  private async stopSound(): Promise<void> {
    try {
      if (this.currentSound) {
        await this.currentSound.stopAsync();
        await this.currentSound.unloadAsync();
        this.currentSound = null;
        this.isPlaying = false;
        console.log('Alarm sound stopped');
      }
    } catch (error) {
      console.error('Error stopping alarm sound:', error);
    }
  }

  // Start vibration pattern
  private async startVibration(vibrationPattern?: string): Promise<void> {
    try {
      // Stop any existing vibration
      this.stopVibration();

      // Start continuous vibration pattern
      const vibrate = async () => {
        try {
          switch (vibrationPattern) {
            case 'light':
              await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
              break;
            case 'medium':
              await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
              break;
            case 'heavy':
              await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
              break;
            default:
              // Default pattern: medium impact
              await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
              break;
          }
        } catch (error) {
          console.error('Error in vibration pattern:', error);
        }
      };

      // Start vibration and repeat every 1 second
      await vibrate();
      this.vibrationInterval = setInterval(vibrate, 1000);

      console.log('Vibration pattern started');
    } catch (error) {
      console.error('Error starting vibration:', error);
    }
  }

  // Stop vibration
  private stopVibration(): void {
    if (this.vibrationInterval) {
      clearInterval(this.vibrationInterval);
      this.vibrationInterval = null;
      console.log('Vibration stopped');
    }
  }

  // Dismiss alarm (stop all alarm activities)
  async dismissAlarm(): Promise<void> {
    try {
      console.log('Dismissing alarm');

      // Stop sound
      await this.stopSound();

      // Stop vibration
      this.stopVibration();

      // Clear notification
      await notificationService.clearAlarmNotification();

      // Reset ringing state
      this.ringingState = {
        isRinging: false,
        alarmId: null,
        alarmName: null,
        soundUri: null,
        vibrationPattern: null,
      };

      console.log('Alarm dismissed successfully');
    } catch (error) {
      console.error('Error dismissing alarm:', error);
    }
  }

  // Get current ringing state
  getRingingState(): AlarmRingingState {
    return { ...this.ringingState };
  }

  // Check if alarm is currently ringing
  isAlarmRinging(): boolean {
    return this.ringingState.isRinging;
  }

  // Get current alarm ID if ringing
  getCurrentAlarmId(): string | null {
    return this.ringingState.alarmId;
  }
}

// Export singleton instance
export const alarmTriggerService = AlarmTriggerService.getInstance();
