import React from 'react';
import { Platform, StyleSheet, Dimensions } from 'react-native';
import { Center } from './StyledView';
import { TitleText, BodyText } from './StyledText';
import { Alarm, LocationCoordinates, MapRegion } from '../types';

const { width, height } = Dimensions.get('window');

interface MapComponentProps {
  region: MapRegion;
  alarms: Alarm[];
  selectedLocation: LocationCoordinates | null;
  defaultRadius: number;
  onRegionChange: (region: MapRegion) => void;
  onMapPress: (event: any) => void;
}

// Web fallback component
const WebMapPlaceholder = ({ alarms }: { alarms: Alarm[] }) => (
  <Center style={styles.map} backgroundColor="gray100">
    <TitleText marginBottom="m">Map View</TitleText>
    <BodyText textAlign="center" marginHorizontal="l">
      Maps are not available on web. Please use the mobile app to create location-based alarms.
    </BodyText>
    <BodyText marginTop="l" textAlign="center" color="textSecondary">
      Current alarms: {alarms.length}
    </BodyText>
  </Center>
);

// Native map component - only loaded on native platforms
const NativeMapComponent = ({
  region,
  alarms,
  selectedLocation,
  defaultRadius,
  onRegionChange,
  onMapPress
}: MapComponentProps) => {
  // This will only be called on native platforms
  try {
    const MapView = require('react-native-maps').default;
    const { Marker, Circle } = require('react-native-maps');

    return (
      <MapView
        style={styles.map}
        region={region}
        onRegionChangeComplete={onRegionChange}
        onPress={onMapPress}
        showsUserLocation={true}
        showsMyLocationButton={true}
      >
        {/* Existing alarms */}
        {alarms.map((alarm) => (
          <React.Fragment key={alarm.id}>
            <Marker
              coordinate={{
                latitude: alarm.latitude,
                longitude: alarm.longitude,
              }}
              title={alarm.name}
              description={`Radius: ${alarm.radius}m`}
              pinColor={alarm.isActive ? 'red' : 'gray'}
            />
            <Circle
              center={{
                latitude: alarm.latitude,
                longitude: alarm.longitude,
              }}
              radius={alarm.radius}
              strokeColor={alarm.isActive ? 'rgba(255, 0, 0, 0.5)' : 'rgba(128, 128, 128, 0.5)'}
              fillColor={alarm.isActive ? 'rgba(255, 0, 0, 0.1)' : 'rgba(128, 128, 128, 0.1)'}
            />
          </React.Fragment>
        ))}

        {/* Selected location for new alarm */}
        {selectedLocation && (
          <React.Fragment>
            <Marker
              coordinate={selectedLocation}
              title="New Alarm Location"
              pinColor="blue"
            />
            <Circle
              center={selectedLocation}
              radius={defaultRadius}
              strokeColor="rgba(0, 122, 255, 0.5)"
              fillColor="rgba(0, 122, 255, 0.1)"
            />
          </React.Fragment>
        )}
      </MapView>
    );
  } catch (error) {
    // Fallback if maps module fails to load
    return <WebMapPlaceholder alarms={alarms} />;
  }
};

export default function MapComponent(props: MapComponentProps) {
  if (Platform.OS === 'web') {
    return <WebMapPlaceholder alarms={props.alarms} />;
  }

  return <NativeMapComponent {...props} />;
}

const styles = StyleSheet.create({
  map: {
    width: width,
    height: height,
  },
});
