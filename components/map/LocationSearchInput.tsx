import React, { useState, useCallback, useRef } from 'react';
import { TextInput, TouchableOpacity, FlatList, Keyboard } from 'react-native';
import * as Location from 'expo-location';
import FontAwesome from '@expo/vector-icons/FontAwesome';

import StyledView from '@/components/StyledView';
import { BodyText, CaptionText } from '@/components/StyledText';
import { useAlarmStore } from '@/store/alarmStore';
import { LocationCoordinates } from '@/types';

interface SearchResult {
  id: string;
  address: string;
  coordinates: LocationCoordinates;
}

interface LocationSearchInputProps {
  onLocationSelect?: (coordinates: LocationCoordinates) => void;
  placeholder?: string;
}

export default function LocationSearchInput({ 
  onLocationSelect,
  placeholder = "Search for a location..." 
}: LocationSearchInputProps) {
  const { setSelectedLocation, setMapRegion } = useAlarmStore();
  const [searchText, setSearchText] = useState('');
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [showResults, setShowResults] = useState(false);
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Debounced search function
  const performSearch = useCallback(async (query: string) => {
    if (query.trim().length < 3) {
      setSearchResults([]);
      setShowResults(false);
      return;
    }

    setIsSearching(true);
    try {
      // Use expo-location's geocodeAsync to convert address to coordinates
      const results = await Location.geocodeAsync(query);
      
      const searchResults: SearchResult[] = results.map((result, index) => ({
        id: `${result.latitude}-${result.longitude}-${index}`,
        address: query, // We could enhance this with reverse geocoding for better addresses
        coordinates: {
          latitude: result.latitude,
          longitude: result.longitude,
        },
      }));

      setSearchResults(searchResults);
      setShowResults(searchResults.length > 0);
    } catch (error) {
      console.error('Search error:', error);
      setSearchResults([]);
      setShowResults(false);
    } finally {
      setIsSearching(false);
    }
  }, []);

  // Handle text input changes with debouncing
  const handleSearchTextChange = useCallback((text: string) => {
    setSearchText(text);

    // Clear existing timeout
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    // Set new timeout for debounced search
    searchTimeoutRef.current = setTimeout(() => {
      performSearch(text);
    }, 500); // 500ms debounce
  }, [performSearch]);

  // Handle selecting a search result
  const handleSelectResult = useCallback((result: SearchResult) => {
    const { coordinates } = result;
    
    // Update selected location in store
    setSelectedLocation(coordinates);
    
    // Create a map region centered on the selected location
    const region = {
      latitude: coordinates.latitude,
      longitude: coordinates.longitude,
      latitudeDelta: 0.01, // Zoom level
      longitudeDelta: 0.01,
    };
    setMapRegion(region);

    // Call optional callback
    onLocationSelect?.(coordinates);

    // Clear search
    setSearchText(result.address);
    setShowResults(false);
    setSearchResults([]);
    Keyboard.dismiss();
  }, [setSelectedLocation, setMapRegion, onLocationSelect]);

  // Handle clearing search
  const handleClearSearch = useCallback(() => {
    setSearchText('');
    setSearchResults([]);
    setShowResults(false);
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }
  }, []);

  // Render search result item
  const renderSearchResult = useCallback(({ item }: { item: SearchResult }) => (
    <TouchableOpacity onPress={() => handleSelectResult(item)}>
      <StyledView
        backgroundColor="white"
        paddingVertical="s"
        paddingHorizontal="m"
        borderBottomWidth={1}
        borderBottomColor="gray200"
      >
        <BodyText numberOfLines={1}>{item.address}</BodyText>
        <CaptionText color="textSecondary">
          {item.coordinates.latitude.toFixed(4)}, {item.coordinates.longitude.toFixed(4)}
        </CaptionText>
      </StyledView>
    </TouchableOpacity>
  ), [handleSelectResult]);

  return (
    <StyledView>
      {/* Search Input */}
      <StyledView
        backgroundColor="white"
        borderRadius="m"
        paddingHorizontal="m"
        paddingVertical="s"
        flexDirection="row"
        alignItems="center"
        shadowColor="black"
        shadowOffset={{ width: 0, height: 2 }}
        shadowOpacity={0.1}
        shadowRadius={4}
        elevation={3}
      >
        <FontAwesome name="search" size={16} color="#666" style={{ marginRight: 8 }} />
        <TextInput
          value={searchText}
          onChangeText={handleSearchTextChange}
          placeholder={placeholder}
          style={{
            flex: 1,
            fontSize: 16,
            paddingVertical: 8,
          }}
          returnKeyType="search"
          onSubmitEditing={() => performSearch(searchText)}
        />
        {(searchText.length > 0 || isSearching) && (
          <TouchableOpacity onPress={handleClearSearch} style={{ marginLeft: 8 }}>
            <FontAwesome 
              name={isSearching ? "spinner" : "times"} 
              size={16} 
              color="#666" 
            />
          </TouchableOpacity>
        )}
      </StyledView>

      {/* Search Results */}
      {showResults && searchResults.length > 0 && (
        <StyledView
          backgroundColor="white"
          borderRadius="m"
          marginTop="xs"
          shadowColor="black"
          shadowOffset={{ width: 0, height: 2 }}
          shadowOpacity={0.1}
          shadowRadius={4}
          elevation={3}
          maxHeight={200}
        >
          <FlatList
            data={searchResults}
            renderItem={renderSearchResult}
            keyExtractor={(item) => item.id}
            showsVerticalScrollIndicator={false}
            keyboardShouldPersistTaps="handled"
          />
        </StyledView>
      )}
    </StyledView>
  );
}
