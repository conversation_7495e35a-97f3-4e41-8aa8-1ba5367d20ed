import React, { useEffect, useState, useRef } from 'react';
import { Platform, Alert, ActivityIndicator, TouchableOpacity } from 'react-native';

import StyledView, { Container, Center } from '@/components/StyledView';
import { TitleText, BodyText } from '@/components/StyledText';
import { useAlarmStore } from '@/store/alarmStore';
import MapComponent, { MapComponentRef } from '@/components/MapComponent.native';
import LocationSearchInput from '@/components/map/LocationSearchInput';
import AlarmConfigurationModal, { AlarmConfig } from '@/components/map/AlarmConfigurationModal';
import { LocationCoordinates } from '@/types';

export default function MapScreen() {
  const {
    alarms,
    addAlarm,
    mapRegion,
    selectedLocation,
    locationPermissionGranted,
    isLocationLoading,
    error,
    initializeLocation,
    requestLocationPermission,
    getCurrentLocation,
    setMapRegion,
    setSelectedLocation,
    startLocationTracking,
    settings,
    backgroundLocationState,
    requestBackgroundPermission,
    initializeGeofencing
  } = useAlarmStore();

  const [isInitialized, setIsInitialized] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [modalRadius, setModalRadius] = useState(settings.defaultRadius);
  const mapRef = useRef<MapComponentRef>(null);

  useEffect(() => {
    const initialize = async () => {
      try {
        await initializeLocation();
        if (locationPermissionGranted) {
          await startLocationTracking();
          // Initialize geofencing for background location tracking
          await initializeGeofencing();
        }
      } catch (error) {
        console.error('Failed to initialize location:', error);
      } finally {
        setIsInitialized(true);
      }
    };

    initialize();
  }, []);

  const handleRequestPermission = async () => {
    const granted = await requestLocationPermission();
    if (granted) {
      await getCurrentLocation();
      await startLocationTracking();

      // Initialize geofencing after location permissions are granted
      await initializeGeofencing();

      // Optionally request background permission for better alarm reliability
      if (!backgroundLocationState.backgroundPermissionGranted) {
        Alert.alert(
          'Background Location Permission',
          'For the most reliable alarm experience, please allow background location access. This ensures alarms work even when the app is closed.',
          [
            { text: 'Not Now', style: 'cancel' },
            {
              text: 'Allow',
              onPress: async () => {
                await requestBackgroundPermission();
              }
            }
          ]
        );
      }
    } else {
      Alert.alert(
        'Location Permission Required',
        'This app needs location access to create location-based alarms. Please enable location permissions in your device settings.',
        [{ text: 'OK' }]
      );
    }
  };

  const handleMapPress = (event: any) => {
    if (!locationPermissionGranted) return;

    const coordinate = event.nativeEvent.coordinate;
    setSelectedLocation(coordinate);
    setModalRadius(settings.defaultRadius);
    setIsModalVisible(true);
  };

  const handleAlarmConfirm = async (config: AlarmConfig) => {
    if (!selectedLocation) return;

    try {
      await addAlarm({
        name: config.name,
        latitude: selectedLocation.latitude,
        longitude: selectedLocation.longitude,
        radius: config.radius,
        isActive: true,
        soundUri: config.soundEnabled ? settings.defaultSound : undefined,
        vibrationPattern: config.vibrationEnabled ? 'default' : undefined,
      });

      setSelectedLocation(null);
      setIsModalVisible(false);
      Alert.alert('Success', 'Alarm created successfully!');
    } catch (error) {
      console.error('Error creating alarm:', error);
      Alert.alert('Error', 'Failed to create alarm. Please try again.');
    }
  };

  const handleAlarmCancel = () => {
    setSelectedLocation(null);
    setIsModalVisible(false);
  };

  const handleLocationSearch = (coordinates: LocationCoordinates) => {
    // Create a map region centered on the searched location
    const region = {
      latitude: coordinates.latitude,
      longitude: coordinates.longitude,
      latitudeDelta: 0.01, // Zoom level
      longitudeDelta: 0.01,
    };

    // Animate the map to the new location
    mapRef.current?.animateToRegion(region, 1000);

    // Set the location and show the modal
    setSelectedLocation(coordinates);
    setModalRadius(settings.defaultRadius);
    setIsModalVisible(true);
  };

  // Show loading while initializing
  if (!isInitialized || isLocationLoading) {
    return (
      <Container>
        <Center flex={1}>
          <ActivityIndicator size="large" />
          <BodyText marginTop="m" textAlign="center">
            {isLocationLoading ? 'Getting your location...' : 'Initializing...'}
          </BodyText>
        </Center>
      </Container>
    );
  }

  // Show web fallback
  if (Platform.OS === 'web') {
    return (
      <Container>
        <Center flex={1} backgroundColor="gray100">
          <TitleText marginBottom="m">Map View</TitleText>
          <BodyText textAlign="center" marginHorizontal="l">
            Maps are not available on web. Please use the mobile app to create location-based alarms.
          </BodyText>
          <BodyText marginTop="l" textAlign="center" color="textSecondary">
            Current alarms: {alarms.length}
          </BodyText>
        </Center>
      </Container>
    );
  }

  // Show permission request if not granted
  if (!locationPermissionGranted) {
    return (
      <Container>
        <Center flex={1} paddingHorizontal="l">
          <TitleText marginBottom="m" textAlign="center">Location Permission Required</TitleText>
          <BodyText textAlign="center" marginBottom="l" color="textSecondary">
            To create location-based alarms, we need access to your location.
          </BodyText>
          <TouchableOpacity onPress={handleRequestPermission}>
            <StyledView
              backgroundColor="primaryLight"
              paddingVertical="m"
              paddingHorizontal="l"
              borderRadius="m"
            >
              <BodyText textAlign="center" color="primary">
                Grant Location Permission
              </BodyText>
            </StyledView>
          </TouchableOpacity>
          {error && (
            <BodyText marginTop="m" textAlign="center" color="error">
              {error}
            </BodyText>
          )}
        </Center>
      </Container>
    );
  }

  // Show map
  return (
    <Container>
      {mapRegion && (
        <>
          <MapComponent
            ref={mapRef}
            region={mapRegion}
            alarms={alarms}
            selectedLocation={selectedLocation}
            defaultRadius={modalRadius}
            onRegionChange={setMapRegion}
            onMapPress={handleMapPress}
          />

          {/* Location Search Input - positioned over the map */}
          <StyledView
            position="absolute"
            top={60}
            left={20}
            right={20}
            zIndex={1}
          >
            <LocationSearchInput
              onLocationSelect={handleLocationSearch}
              placeholder="Search for a location..."
            />
          </StyledView>
        </>
      )}

      {/* Alarm Configuration Modal */}
      <AlarmConfigurationModal
        visible={isModalVisible}
        location={selectedLocation}
        defaultRadius={settings.defaultRadius}
        onConfirm={handleAlarmConfirm}
        onCancel={handleAlarmCancel}
        onRadiusChange={setModalRadius}
      />

      {error && (
        <StyledView
          position="absolute"
          top={60}
          left={20}
          right={20}
          backgroundColor="gray100"
          padding="m"
          borderRadius="s"
        >
          <BodyText color="error" textAlign="center">
            {error}
          </BodyText>
        </StyledView>
      )}
    </Container>
  );
}
