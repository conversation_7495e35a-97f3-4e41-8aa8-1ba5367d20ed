import React, { useEffect, useState } from 'react';
import { Alert, Pressable, StatusBar, Dimensions } from 'react-native';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';
import FontAwesome from '@expo/vector-icons/FontAwesome';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withRepeat,
  withTiming,
  interpolate,
} from 'react-native-reanimated';

import { Container, Column, Row } from '@/components/StyledView';
import { TitleText, BodyText } from '@/components/StyledText';
import { useAlarmStore } from '@/store/alarmStore';
import { alarmTriggerService } from '@/services/AlarmTriggerService';

const { width, height } = Dimensions.get('window');

export default function AlarmRingingScreen() {
  const router = useRouter();
  const { alarmId } = useLocalSearchParams<{ alarmId: string }>();
  const { alarms, dismissAlarm } = useAlarmStore();
  const [alarm, setAlarm] = useState(alarms.find(a => a.id === alarmId));

  // Animation values
  const pulseAnimation = useSharedValue(0);
  const shakeAnimation = useSharedValue(0);

  useEffect(() => {
    // Find the alarm
    const foundAlarm = alarms.find(a => a.id === alarmId);
    setAlarm(foundAlarm);

    if (!foundAlarm) {
      console.warn('Alarm not found:', alarmId);
      router.back();
      return;
    }

    // Start animations
    pulseAnimation.value = withRepeat(
      withTiming(1, { duration: 1000 }),
      -1,
      true
    );

    shakeAnimation.value = withRepeat(
      withTiming(1, { duration: 100 }),
      -1,
      true
    );

    // Prevent going back accidentally
    const unsubscribe = router.addListener('beforeRemove', (e) => {
      // Prevent default behavior of leaving the screen
      e.preventDefault();

      // Show confirmation dialog
      Alert.alert(
        'Dismiss Alarm',
        'Are you sure you want to dismiss this alarm?',
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Dismiss',
            style: 'destructive',
            onPress: () => handleDismissAlarm(),
          },
        ]
      );
    });

    return () => {
      unsubscribe();
    };
  }, [alarmId, alarms]);

  const handleDismissAlarm = async () => {
    try {
      if (!alarm) return;

      // Dismiss the alarm (stop sound, vibration, clear notification)
      await alarmTriggerService.dismissAlarm();

      // Deactivate the alarm in the store
      await dismissAlarm(alarm.id);

      // Navigate back to the main app
      router.replace('/(tabs)/activeAlarms');
    } catch (error) {
      console.error('Error dismissing alarm:', error);
      Alert.alert('Error', 'Failed to dismiss alarm. Please try again.');
    }
  };

  const handleSnooze = () => {
    // For now, just dismiss the alarm
    // In the future, this could implement snooze functionality
    Alert.alert(
      'Snooze',
      'Snooze functionality will be implemented in a future update. Dismissing alarm for now.',
      [
        { text: 'OK', onPress: handleDismissAlarm },
      ]
    );
  };

  // Animated styles
  const pulseStyle = useAnimatedStyle(() => {
    const scale = interpolate(pulseAnimation.value, [0, 1], [1, 1.2]);
    return {
      transform: [{ scale }],
    };
  });

  const shakeStyle = useAnimatedStyle(() => {
    const translateX = interpolate(shakeAnimation.value, [0, 0.25, 0.5, 0.75, 1], [0, -10, 0, 10, 0]);
    return {
      transform: [{ translateX }],
    };
  });

  if (!alarm) {
    return (
      <Container flex={1} justifyContent="center" alignItems="center">
        <TitleText>Alarm not found</TitleText>
        <BodyText marginTop="m">The alarm could not be found.</BodyText>
      </Container>
    );
  }

  return (
    <>
      <StatusBar barStyle="light-content" backgroundColor="#FF3B30" />
      <LinearGradient
        colors={['#FF3B30', '#FF6B6B', '#FF3B30']}
        style={{ flex: 1 }}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <Container flex={1} justifyContent="center" alignItems="center" padding="xl">
          {/* Alarm Icon with Pulse Animation */}
          <Animated.View style={[pulseStyle, { marginBottom: 40 }]}>
            <FontAwesome name="bell" size={120} color="white" />
          </Animated.View>

          {/* Alarm Name with Shake Animation */}
          <Animated.View style={shakeStyle}>
            <TitleText
              fontSize={32}
              color="white"
              textAlign="center"
              marginBottom="m"
              fontWeight="bold"
            >
              ALARM!
            </TitleText>
            <TitleText
              fontSize={24}
              color="white"
              textAlign="center"
              marginBottom="xl"
            >
              {alarm.name}
            </TitleText>
          </Animated.View>

          {/* Location Info */}
          <Column alignItems="center" marginBottom="xl">
            <BodyText color="white" textAlign="center" marginBottom="s">
              You've entered the alarm area
            </BodyText>
            <Row alignItems="center">
              <FontAwesome name="map-marker" size={16} color="white" />
              <BodyText color="white" marginLeft="s">
                {alarm.latitude.toFixed(4)}, {alarm.longitude.toFixed(4)}
              </BodyText>
            </Row>
          </Column>

          {/* Action Buttons */}
          <Column width="100%" alignItems="center">
            {/* Dismiss Button */}
            <Pressable
              onPress={handleDismissAlarm}
              style={({ pressed }) => ({
                backgroundColor: pressed ? 'rgba(255, 255, 255, 0.8)' : 'white',
                paddingHorizontal: 60,
                paddingVertical: 20,
                borderRadius: 50,
                marginBottom: 20,
                minWidth: 200,
                alignItems: 'center',
                shadowColor: '#000',
                shadowOffset: { width: 0, height: 4 },
                shadowOpacity: 0.3,
                shadowRadius: 8,
                elevation: 8,
              })}
            >
              <Row alignItems="center">
                <FontAwesome name="stop" size={24} color="#FF3B30" />
                <TitleText
                  color="#FF3B30"
                  marginLeft="m"
                  fontSize={20}
                  fontWeight="bold"
                >
                  DISMISS
                </TitleText>
              </Row>
            </Pressable>

            {/* Snooze Button */}
            <Pressable
              onPress={handleSnooze}
              style={({ pressed }) => ({
                backgroundColor: pressed ? 'rgba(255, 255, 255, 0.2)' : 'rgba(255, 255, 255, 0.1)',
                paddingHorizontal: 40,
                paddingVertical: 15,
                borderRadius: 25,
                borderWidth: 2,
                borderColor: 'white',
                minWidth: 150,
                alignItems: 'center',
              })}
            >
              <Row alignItems="center">
                <FontAwesome name="clock-o" size={20} color="white" />
                <BodyText
                  color="white"
                  marginLeft="s"
                  fontSize={16}
                  fontWeight="600"
                >
                  SNOOZE
                </BodyText>
              </Row>
            </Pressable>
          </Column>

          {/* Instructions */}
          <BodyText
            color="rgba(255, 255, 255, 0.8)"
            textAlign="center"
            marginTop="xl"
            fontSize={14}
          >
            Tap "DISMISS" to stop the alarm and deactivate it
          </BodyText>
        </Container>
      </LinearGradient>
    </>
  );
}
