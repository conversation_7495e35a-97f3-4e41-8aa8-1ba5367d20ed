import * as TaskManager from 'expo-task-manager';
import * as Location from 'expo-location';
import { GeofenceEvent, AlarmTriggerData } from '@/types';
import { alarmTriggerService } from '@/services/AlarmTriggerService';

// Task name constant - this will be used to identify the background task
export const LOCATION_TASK_NAME = 'background-location-task';

// Define the background task for handling geofence events
TaskManager.defineTask(LOCATION_TASK_NAME, async ({ data, error }) => {
  if (error) {
    console.error('Background location task error:', error);
    return;
  }

  if (data) {
    const { eventType, region } = data as any;

    try {
      // Handle geofence enter events
      if (eventType === Location.GeofencingEventType.Enter) {
        console.log('Geofence entered:', region);

        // Create the geofence event
        const geofenceEvent: GeofenceEvent = {
          eventType: 'enter',
          region: {
            identifier: region.identifier,
            latitude: region.latitude,
            longitude: region.longitude,
            radius: region.radius,
          },
        };

        // Trigger alarm using the AlarmTriggerService
        await triggerAlarmFromGeofence(geofenceEvent);

        // Log the event for debugging
        console.log('Alarm triggered for region:', region.identifier);
      }

      // Handle geofence exit events (if needed in the future)
      if (eventType === Location.GeofencingEventType.Exit) {
        console.log('Geofence exited:', region);
        // Currently not handling exit events, but this is where you would
        // add logic for stopping alarms or other exit-related actions
      }
    } catch (taskError) {
      console.error('Error processing geofence event:', taskError);
    }
  }
});

// Function to trigger alarm when geofence is entered
async function triggerAlarmFromGeofence(event: GeofenceEvent): Promise<void> {
  try {
    // Get alarm details from the region identifier (alarm ID)
    const alarmId = event.region.identifier;

    // Try to get alarm details from AsyncStorage (since we can't access Zustand store in background task)
    const alarmTriggerData: AlarmTriggerData = await getAlarmDataFromStorage(alarmId, event);

    // Trigger the alarm using the AlarmTriggerService
    await alarmTriggerService.triggerAlarm(alarmTriggerData);

  } catch (error) {
    console.error('Error triggering alarm from geofence:', error);
  }
}

// Helper function to get alarm data from AsyncStorage
async function getAlarmDataFromStorage(alarmId: string, event: GeofenceEvent): Promise<AlarmTriggerData> {
  try {
    // Import AsyncStorage dynamically to avoid issues
    const AsyncStorage = await import('@react-native-async-storage/async-storage');

    // Try to get the alarm data from storage
    const storedAlarms = await AsyncStorage.default.getItem('alarm-store');

    if (storedAlarms) {
      const parsedData = JSON.parse(storedAlarms);
      const alarms = parsedData.state?.alarms || [];
      const alarm = alarms.find((a: any) => a.id === alarmId);

      if (alarm) {
        return {
          alarmId,
          alarmName: alarm.name,
          soundUri: alarm.soundUri,
          vibrationPattern: alarm.vibrationPattern,
          latitude: event.region.latitude,
          longitude: event.region.longitude,
        };
      }
    }

    // Fallback if alarm not found in storage
    return {
      alarmId,
      alarmName: `Location Alarm`,
      latitude: event.region.latitude,
      longitude: event.region.longitude,
    };
  } catch (error) {
    console.error('Error getting alarm data from storage:', error);

    // Fallback alarm data
    return {
      alarmId,
      alarmName: `Location Alarm`,
      latitude: event.region.latitude,
      longitude: event.region.longitude,
    };
  }
}

// Function to check if the background task is registered
export async function isTaskRegistered(): Promise<boolean> {
  return await TaskManager.isTaskRegisteredAsync(LOCATION_TASK_NAME);
}

// Function to get task status
export async function getTaskStatus(): Promise<any> {
  try {
    return await TaskManager.getTaskOptionsAsync(LOCATION_TASK_NAME);
  } catch (error) {
    console.error('Error getting task status:', error);
    return null;
  }
}

// Function to unregister the background task (useful for cleanup)
export async function unregisterTask(): Promise<void> {
  try {
    if (await TaskManager.isTaskRegisteredAsync(LOCATION_TASK_NAME)) {
      await TaskManager.unregisterTaskAsync(LOCATION_TASK_NAME);
      console.log('Background location task unregistered');
    }
  } catch (error) {
    console.error('Error unregistering background task:', error);
  }
}
