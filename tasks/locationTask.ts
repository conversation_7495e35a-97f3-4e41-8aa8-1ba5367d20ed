import * as TaskManager from 'expo-task-manager';
import * as Location from 'expo-location';
import * as Notifications from 'expo-notifications';
import { GeofenceEvent } from '@/types';

// Task name constant - this will be used to identify the background task
export const LOCATION_TASK_NAME = 'background-location-task';

// Define the background task for handling geofence events
TaskManager.defineTask(LOCATION_TASK_NAME, async ({ data, error }) => {
  if (error) {
    console.error('Background location task error:', error);
    return;
  }

  if (data) {
    const { eventType, region } = data as any;

    try {
      // Handle geofence enter events
      if (eventType === Location.GeofencingEventType.Enter) {
        console.log('Geofence entered:', region);

        // Create the geofence event
        const geofenceEvent: GeofenceEvent = {
          eventType: 'enter',
          region: {
            identifier: region.identifier,
            latitude: region.latitude,
            longitude: region.longitude,
            radius: region.radius,
          },
        };

        // Trigger alarm notification
        await triggerAlarmNotification(geofenceEvent);

        // Log the event for debugging
        console.log('Alarm triggered for region:', region.identifier);
      }

      // Handle geofence exit events (if needed in the future)
      if (eventType === Location.GeofencingEventType.Exit) {
        console.log('Geofence exited:', region);
        // Currently not handling exit events, but this is where you would
        // add logic for stopping alarms or other exit-related actions
      }
    } catch (taskError) {
      console.error('Error processing geofence event:', taskError);
    }
  }
});

// Function to trigger alarm notification when geofence is entered
async function triggerAlarmNotification(event: GeofenceEvent): Promise<void> {
  try {
    // Check if notifications are permitted
    const { status } = await Notifications.getPermissionsAsync();
    if (status !== 'granted') {
      console.warn('Notification permissions not granted');
      return;
    }

    // Get alarm details from the region identifier (alarm ID)
    const alarmId = event.region.identifier;

    // Schedule immediate notification
    await Notifications.scheduleNotificationAsync({
      content: {
        title: '🚨 Location Alarm!',
        body: `You've entered the area for alarm: ${alarmId}`,
        sound: true,
        priority: Notifications.AndroidNotificationPriority.HIGH,
        vibrate: [0, 250, 250, 250],
        data: {
          alarmId,
          eventType: event.eventType,
          latitude: event.region.latitude,
          longitude: event.region.longitude,
        },
      },
      trigger: null, // Immediate notification
    });

    // TODO: In Phase 5, this will be replaced with more sophisticated alarm triggering
    // that includes custom sounds, vibration patterns, and alarm screen navigation

  } catch (error) {
    console.error('Error triggering alarm notification:', error);
  }
}

// Function to check if the background task is registered
export async function isTaskRegistered(): Promise<boolean> {
  return await TaskManager.isTaskRegisteredAsync(LOCATION_TASK_NAME);
}

// Function to get task status
export async function getTaskStatus(): Promise<any> {
  try {
    return await TaskManager.getTaskOptionsAsync(LOCATION_TASK_NAME);
  } catch (error) {
    console.error('Error getting task status:', error);
    return null;
  }
}

// Function to unregister the background task (useful for cleanup)
export async function unregisterTask(): Promise<void> {
  try {
    if (await TaskManager.isTaskRegisteredAsync(LOCATION_TASK_NAME)) {
      await TaskManager.unregisterTaskAsync(LOCATION_TASK_NAME);
      console.log('Background location task unregistered');
    }
  } catch (error) {
    console.error('Error unregistering background task:', error);
  }
}
