import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import uuid from 'react-native-uuid';
import {
  Alarm,
  UserSettings,
  LocationCoordinates,
  MapRegion,
  BackgroundLocationState
} from '@/types';
import { locationService, LocationServiceState } from '@/services/LocationService';
import backgroundLocationService from '@/services/BackgroundLocationService';

interface AlarmStore {
  // State
  alarms: Alarm[];
  settings: UserSettings;
  isLoading: boolean;
  error: string | null;

  // Location state
  currentLocation: LocationCoordinates | null;
  mapRegion: MapRegion | null;
  selectedLocation: LocationCoordinates | null;
  locationPermissionGranted: boolean;
  isLocationLoading: boolean;

  // Background location state
  backgroundLocationState: BackgroundLocationState;

  // Actions
  addAlarm: (alarm: Omit<Alarm, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>;
  updateAlarm: (id: string, updates: Partial<Alarm>) => Promise<void>;
  deleteAlarm: (id: string) => Promise<void>;
  toggleAlarm: (id: string) => Promise<void>;
  dismissAlarm: (id: string) => Promise<void>;
  loadAlarms: () => Promise<void>;
  updateSettings: (settings: Partial<UserSettings>) => void;
  clearError: () => void;

  // Location actions
  initializeLocation: () => Promise<void>;
  requestLocationPermission: () => Promise<boolean>;
  getCurrentLocation: () => Promise<void>;
  setMapRegion: (region: MapRegion) => void;
  setSelectedLocation: (location: LocationCoordinates | null) => void;
  startLocationTracking: () => Promise<void>;
  stopLocationTracking: () => Promise<void>;

  // Background location actions
  requestBackgroundPermission: () => Promise<boolean>;
  initializeGeofencing: () => Promise<void>;
  updateGeofencing: () => Promise<void>;
  stopGeofencing: () => Promise<void>;
}

const defaultSettings: UserSettings = {
  defaultRadius: 100, // 100 meters
  defaultSound: 'default',
  vibrationEnabled: true,
  notificationsEnabled: true,
  theme: 'auto',
};

export const useAlarmStore = create<AlarmStore>()(
  persist(
    (set, get) => ({
      // Initial state
      alarms: [],
      settings: defaultSettings,
      isLoading: false,
      error: null,

      // Location state
      currentLocation: null,
      mapRegion: null,
      selectedLocation: null,
      locationPermissionGranted: false,
      isLocationLoading: false,

      // Background location state
      backgroundLocationState: {
        isGeofencingActive: false,
        activeGeofences: [],
        backgroundPermissionGranted: false,
        error: null,
      },

      // Actions
      addAlarm: async (alarmData) => {
        const newAlarm: Alarm = {
          ...alarmData,
          id: uuid.v4() as string,
          createdAt: new Date(),
          updatedAt: new Date(),
        };

        set((state) => ({
          alarms: [...state.alarms, newAlarm],
          error: null,
        }));

        // Update geofencing if the new alarm is active
        if (newAlarm.isActive) {
          try {
            const { alarms } = get();
            await backgroundLocationService.updateGeofencing(alarms);
          } catch (error) {
            console.error('Error updating geofencing after adding alarm:', error);
          }
        }
      },

      updateAlarm: async (id, updates) => {
        set((state) => ({
          alarms: state.alarms.map((alarm) =>
            alarm.id === id
              ? { ...alarm, ...updates, updatedAt: new Date() }
              : alarm
          ),
          error: null,
        }));

        // Update geofencing after alarm update
        try {
          const { alarms } = get();
          await backgroundLocationService.updateGeofencing(alarms);
        } catch (error) {
          console.error('Error updating geofencing after updating alarm:', error);
        }
      },

      deleteAlarm: async (id) => {
        set((state) => ({
          alarms: state.alarms.filter((alarm) => alarm.id !== id),
          error: null,
        }));

        // Update geofencing after alarm deletion
        try {
          const { alarms } = get();
          await backgroundLocationService.updateGeofencing(alarms);
        } catch (error) {
          console.error('Error updating geofencing after deleting alarm:', error);
        }
      },

      toggleAlarm: async (id) => {
        set((state) => ({
          alarms: state.alarms.map((alarm) =>
            alarm.id === id
              ? { ...alarm, isActive: !alarm.isActive, updatedAt: new Date() }
              : alarm
          ),
          error: null,
        }));

        // Update geofencing after toggling alarm
        try {
          const { alarms } = get();
          await backgroundLocationService.updateGeofencing(alarms);
        } catch (error) {
          console.error('Error updating geofencing after toggling alarm:', error);
        }
      },

      dismissAlarm: async (id) => {
        // Deactivate the alarm when dismissed
        set((state) => ({
          alarms: state.alarms.map((alarm) =>
            alarm.id === id
              ? { ...alarm, isActive: false, updatedAt: new Date() }
              : alarm
          ),
          error: null,
        }));

        // Update geofencing after dismissing alarm
        try {
          const { alarms } = get();
          await backgroundLocationService.updateGeofencing(alarms);
        } catch (error) {
          console.error('Error updating geofencing after dismissing alarm:', error);
        }
      },

      loadAlarms: async () => {
        set({ isLoading: true, error: null });
        try {
          // For now, alarms are persisted automatically by zustand
          // In the future, this could load from a remote API
          set({ isLoading: false });
        } catch (error) {
          set({
            isLoading: false,
            error: error instanceof Error ? error.message : 'Failed to load alarms'
          });
        }
      },

      updateSettings: (newSettings) => {
        set((state) => ({
          settings: { ...state.settings, ...newSettings },
          error: null,
        }));
      },

      clearError: () => {
        set({ error: null });
      },

      // Location actions
      initializeLocation: async () => {
        set({ isLocationLoading: true, error: null });
        try {
          // Check permissions first
          const permissions = await locationService.checkPermissions();
          set({ locationPermissionGranted: permissions.granted });

          if (permissions.granted) {
            // Get current location
            const location = await locationService.getCurrentLocation();
            const region = locationService.createMapRegion(location);
            set({
              currentLocation: location,
              mapRegion: region,
              isLocationLoading: false
            });
          } else {
            set({ isLocationLoading: false });
          }
        } catch (error) {
          set({
            isLocationLoading: false,
            error: error instanceof Error ? error.message : 'Failed to initialize location'
          });
        }
      },

      requestLocationPermission: async () => {
        set({ isLocationLoading: true, error: null });
        try {
          const permissions = await locationService.requestPermissions();
          set({
            locationPermissionGranted: permissions.granted,
            isLocationLoading: false
          });
          return permissions.granted;
        } catch (error) {
          set({
            isLocationLoading: false,
            error: error instanceof Error ? error.message : 'Failed to request location permission'
          });
          return false;
        }
      },

      getCurrentLocation: async () => {
        set({ isLocationLoading: true, error: null });
        try {
          const location = await locationService.getCurrentLocation();
          const region = locationService.createMapRegion(location);
          set({
            currentLocation: location,
            mapRegion: region,
            isLocationLoading: false
          });
        } catch (error) {
          set({
            isLocationLoading: false,
            error: error instanceof Error ? error.message : 'Failed to get current location'
          });
        }
      },

      setMapRegion: (region) => {
        set({ mapRegion: region });
      },

      setSelectedLocation: (location) => {
        set({ selectedLocation: location });
      },

      startLocationTracking: async () => {
        try {
          await locationService.startWatchingLocation();

          // Subscribe to location updates
          locationService.subscribe((locationState: LocationServiceState) => {
            set({
              currentLocation: locationState.currentLocation,
              locationPermissionGranted: locationState.permissionStatus?.granted || false,
              error: locationState.error
            });
          });
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Failed to start location tracking'
          });
        }
      },

      stopLocationTracking: async () => {
        try {
          await locationService.stopWatchingLocation();
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Failed to stop location tracking'
          });
        }
      },

      // Background location actions
      requestBackgroundPermission: async () => {
        try {
          const permissions = await backgroundLocationService.requestBackgroundPermissions();
          set((state) => ({
            backgroundLocationState: {
              ...state.backgroundLocationState,
              backgroundPermissionGranted: permissions.granted,
              error: null,
            }
          }));
          return permissions.granted;
        } catch (error) {
          set((state) => ({
            backgroundLocationState: {
              ...state.backgroundLocationState,
              error: error instanceof Error ? error.message : 'Failed to request background permission'
            }
          }));
          return false;
        }
      },

      initializeGeofencing: async () => {
        try {
          // Check background permissions first
          const permissions = await backgroundLocationService.checkBackgroundPermissions();

          set((state) => ({
            backgroundLocationState: {
              ...state.backgroundLocationState,
              backgroundPermissionGranted: permissions.granted,
              error: null,
            }
          }));

          // Subscribe to background location service updates
          backgroundLocationService.subscribe((backgroundState: BackgroundLocationState) => {
            set(() => ({
              backgroundLocationState: backgroundState
            }));
          });

          // Start geofencing for active alarms if permissions are granted
          if (permissions.granted) {
            const { alarms } = get();
            await backgroundLocationService.startGeofencing(alarms);
          }
        } catch (error) {
          set((state) => ({
            backgroundLocationState: {
              ...state.backgroundLocationState,
              error: error instanceof Error ? error.message : 'Failed to initialize geofencing'
            }
          }));
        }
      },

      updateGeofencing: async () => {
        try {
          const { alarms } = get();
          await backgroundLocationService.updateGeofencing(alarms);
        } catch (error) {
          set((state) => ({
            backgroundLocationState: {
              ...state.backgroundLocationState,
              error: error instanceof Error ? error.message : 'Failed to update geofencing'
            }
          }));
        }
      },

      stopGeofencing: async () => {
        try {
          await backgroundLocationService.stopGeofencing();
        } catch (error) {
          set((state) => ({
            backgroundLocationState: {
              ...state.backgroundLocationState,
              error: error instanceof Error ? error.message : 'Failed to stop geofencing'
            }
          }));
        }
      },
    }),
    {
      name: 'alarm-store',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        alarms: state.alarms,
        settings: state.settings,
        // Don't persist location state - it should be fresh on each app start
      }),
    }
  )
);
